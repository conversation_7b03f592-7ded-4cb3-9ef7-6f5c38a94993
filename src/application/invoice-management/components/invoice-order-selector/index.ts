import { computed, defineComponent, reactive, ref, toRefs, type PropType } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { type FormInstance } from 'ant-design-vue';
import { timeFilter, generateUniqueId } from '@/utils/utils';
import type { HistoryOrderResponse, InvoiceOrderSelectorState, OrderSelectItemModel } from './types';
import { MATH } from '@paas/paas-library';

export default defineComponent({
	name: 'InvoiceOrderSelector',
	components: {
		PlusOutlined
	},
	props: {
		/** 历史订单列表 */
		historyOrderOptions: {
			type: Array as PropType<HistoryOrderResponse[]>,
			default: () => []
		}
	},
	emits: ['change', 'amount-change'],
	setup(props, { emit }) {
		// 表单引用
		const formRef = ref<FormInstance>();

		// 创建默认订单选择项
		const createDefaultOrderItem = (): OrderSelectItemModel => ({
			id: generateUniqueId(),
			selectedOrderNo: undefined,
			invoiceAmount: 0
		});

		const state = reactive<InvoiceOrderSelectorState>({
			orderSelectList: [createDefaultOrderItem()]
		});

		// 计算属性
		/** 格式化的历史订单列表 */
		const formatHistoryOrderList = computed(() => {
			return props.historyOrderOptions
				.filter(item => item.enableApplyBlueInvoice)
				.map(item => ({
					...item,
					formatName: `${timeFilter(item.paidTime, 'YYYY年MM月DD日')}购买${item.goodsName}${
						item.orderAmount
					}元`
				}));
		});

		/** 总开票金额 */
		const totalInvoiceAmount = computed(() => {
			return state.orderSelectList.reduce((total, item) => MATH.numberAdd(total, item.invoiceAmount), 0);
		});

		/**
		 * 为指定索引的下拉框获取过滤后的选项列表
		 * 排除已被其他下拉框选中的订单
		 */
		const getFilteredOptionsForIndex = computed(() => {
			return (index: number) => {
				// 获取其他下拉框已选中的订单号
				const selectedOrderNos = state.orderSelectList
					.map((item, idx) => (idx !== index ? item.selectedOrderNo : null))
					.filter(orderNo => orderNo !== null && orderNo !== undefined);

				// 过滤掉已被选中的订单
				return formatHistoryOrderList.value.filter(option => !selectedOrderNos.includes(option.orderNo));
			};
		});

		/** 表单数据模型 */
		const formModel = computed(() => ({
			orderSelectList: state.orderSelectList
		}));

		// 统一的事件触发方法
		const emitEvents = () => {
			emit('change', [...state.orderSelectList]);
			emit('amount-change', totalInvoiceAmount.value);
		};

		// 方法
		/**
		 * 添加订单选择项
		 */
		const addOrderSelect = () => {
			state.orderSelectList.push(createDefaultOrderItem());
			emitEvents();
		};

		/**
		 * 删除订单选择项
		 * @param index 索引
		 */
		const removeOrderSelect = (index: number) => {
			if (state.orderSelectList.length > 1) {
				state.orderSelectList.splice(index, 1);
				emitEvents();
				// 清除被删除项的校验错误
				setTimeout(() => {
					formRef.value?.clearValidate();
				}, 0);
			}
		};

		/**
		 * 订单选择变化处理
		 * @param value 选中的订单号
		 * @param option 选中的订单选项
		 * @param index 索引
		 */
		const onChangeOrder = (value: string, option: HistoryOrderResponse, index: number) => {
			const item = state.orderSelectList[index];
			if (item) {
				item.selectedOrderNo = value;
				item.invoiceAmount = option?.invoiceableAmount || 0;
			}
			emitEvents();
		};

		/**
		 * 获取选中的订单列表
		 */
		const getSelectedOrders = (): OrderSelectItemModel[] => {
			return state.orderSelectList.filter(item => item.selectedOrderNo);
		};

		/**
		 * 重置组件状态
		 */
		const reset = () => {
			state.orderSelectList = [createDefaultOrderItem()];
			emitEvents();
		};

		/**
		 * 设置订单选择列表
		 * @param orders 订单列表
		 */
		const setOrderSelectList = (orders: OrderSelectItemModel[]) => {
			state.orderSelectList = orders.length > 0 ? orders : [createDefaultOrderItem()];
			emitEvents();
		};

		/**
		 * 校验所有下拉框
		 * @returns Promise<boolean> 校验是否通过
		 */
		const validateAll = async (): Promise<boolean> => {
			try {
				await formRef.value?.validate();
				return true;
			} catch {
				return false;
			}
		};

		/**
		 * 清除所有校验错误
		 */
		const clearValidate = () => {
			formRef.value?.clearValidate();
		};

		return {
			formRef,
			...toRefs(state),
			formModel,
			formatHistoryOrderList,
			totalInvoiceAmount,
			getFilteredOptionsForIndex,
			addOrderSelect,
			removeOrderSelect,
			onChangeOrder,
			getSelectedOrders,
			reset,
			setOrderSelectList,
			validateAll,
			clearValidate
		};
	}
});
