.invoice-order-selector {
	.order-select-item {
		margin-bottom: 16px;
		width: 100%;

		.order-select-row {
			width: 100%;
			display: flex;
			align-items: flex-start;

			.order-select-wrapper {
				width: 0;
				flex: 1;

				.order-select-form-item {
					margin-bottom: 0;
				}

				.order-select {
					width: 100%;
				}
			}

			.action-buttons {
				display: flex;
				align-items: center;
				height: 32px;

				.remove-btn {
					color: #ff4d4f;
					border: none;
					padding: 4px 8px;
					height: auto;
					font-size: 12px;

					&:hover {
						color: #ff7875;
						background-color: #fff2f0;
					}
				}
			}
		}

		&:last-child {
			margin-bottom: 0;
		}
	}

	.add-button-wrapper {
		margin-top: 16px;

		.add-btn {
			width: 100%;
			height: 32px;
			border: 1px solid rgba(0, 0, 0, 0.15);
			color: #666;
			display: flex;
			align-items: center;
			justify-content: center;

			&:hover {
				border-color: #1890ff;
				color: #1890ff;
				background-color: #f0f8ff;
			}

			&:focus {
				border-color: #1890ff;
				color: #1890ff;
			}
		}
	}
}
