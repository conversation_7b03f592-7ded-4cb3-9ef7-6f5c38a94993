import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import { MESSAGE_TYPE, MUtils, PhoneRegExp } from '@paas/paas-library';
import { type FormInstance } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import {
	BANK_ACCOUNT_PATTERN,
	BANK_NAME_PATTERN,
	BuyerTypeEnum,
	EMAIL_PATTERN,
	HEADER_TYPE_OPTIONS,
	INVOICE_TYPE_OPTIONS,
	InvoiceTypeEnum,
	MAX_ADDRESS_LENGTH,
	PHONE_PATTERN
} from '../../constants';
import { FetchHistoryOrderInvoiceStore, GetApplyInvoicePreviewStore, SubmitInvoiceApplicationStore } from '../../store';
import type { ApplyInvoicePreviewResponse, InvoiceApplicationFormModel } from '../../types';
import { StateModel } from '@/application/invoice-management/components/Invoice-application-dialog/types';
import { GetHistoryOrderListStore } from '@/application/invoice-management/components/Invoice-application-dialog/store';
import { useUserProfileStore } from '@/pinia/user-profile';
import InvoiceOrderSelector from '@/application/invoice-management/components/Invoice-order-selector/index.vue';
import type { OrderSelectItemModel } from '@/application/invoice-management/components/Invoice-order-selector/types';

export default defineComponent({
	name: 'InvoiceApplicationDialog',
	components: {
		ExclamationCircleFilled,
		InvoiceOrderSelector
	},
	emits: ['close', 'success'],
	setup(props, { emit }) {
		const userProfileStore = useUserProfileStore();

		const formRef = ref<FormInstance>();
		const orderSelectorRef = ref<InstanceType<typeof InvoiceOrderSelector>>();

		const state = reactive<StateModel>({
			visible: false,
			submitLoading: false,
			// 订单号列表
			orderNos: [],
			// 是否是2021年1月1日之前购买的老订单
			isHistoryOrder: false,
			// 历史订单列表
			historyOrderOptions: [],
			// 订单选择列表
			orderSelectList: [],
			// 表单数据
			formData: {
				invoiceAmount: 0,
				buyerType: BuyerTypeEnum.COMPANY,
				invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON,
				buyerName: '',
				buyerIdNum: '',
				receiveEmail: '',
				buyerBankName: '',
				buyerBankAccount: '',
				buyerAddress: '',
				buyerMobileNum: '',
				orderNos: [],
				orderSelectList: [] // 添加到表单数据中
			}
		});

		// 计算属性
		const computeds = {
			// 是否为专用发票
			isSpecialInvoice: computed(() => {
				return state.formData.invoiceType === InvoiceTypeEnum.ELECTRONIC_SPECIAL;
			}),

			// 可用的发票类型选项
			availableInvoiceTypes: computed(() => {
				// 若抬头类型为个人，只能选择普通发票
				if (state.formData.buyerType === BuyerTypeEnum.PERSONAL) {
					return INVOICE_TYPE_OPTIONS.filter(option => option.value === InvoiceTypeEnum.ELECTRONIC_COMMON);
				}
				// 企业类型可以选择所有发票类型
				return INVOICE_TYPE_OPTIONS;
			}),

			// 是否可以编辑开票金额（老订单且只有一条数据时可编辑）
			canEditInvoiceAmount: computed(() => {
				return (
					(state.isHistoryOrder && state.orderSelectList.length === 1) ||
					(!state.isHistoryOrder && state.orderNos.length <= 1)
				);
			}),

			// 最大开票金额（当前选中订单的可开票金额）
			maxInvoiceAmount: computed(() => {
				const firstOrder = state.orderSelectList[0];
				if (!firstOrder?.selectedOrderNo) {
					return 100;
				}

				const selectedOrder = state.historyOrderOptions.find(
					order => order.orderNo === firstOrder.selectedOrderNo
				);

				return selectedOrder?.invoiceableAmount || 100;
			}),

			// 发票的额外验证规则
			specialInvoiceRules: computed(() => {
				return {
					// 开票金额校验（仅老订单时校验）
					// invoiceAmount: [
					// 	{
					// 		required: state.isOldOrder,
					// 		trigger: ['change', 'blur'],
					// 		validator(rule: any, value: number) {
					// 			if (!state.isOldOrder) {
					// 				return Promise.resolve();
					// 			}
					// 			if (!value || value <= 0) {
					// 				return Promise.reject('请输入有效的开票金额');
					// 			}
					// 			return Promise.resolve();
					// 		}
					// 	}
					// ],

					buyerBankName: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入开户银行');
								}
								if (value && !BANK_NAME_PATTERN.test(value)) {
									return Promise.reject('开户银行只能包含中文、英文和常用符号');
								}
								return Promise.resolve();
							}
						}
					],
					buyerBankAccount: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入银行账号');
								}
								if (value && !BANK_ACCOUNT_PATTERN.test(value)) {
									return Promise.reject('银行账号只能包含英文和数字');
								}
								return Promise.resolve();
							}
						}
					],
					buyerAddress: [
						{
							required: computeds.isSpecialInvoice.value,
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入企业地址');
								}
								return Promise.resolve();
							}
						},
						{ max: MAX_ADDRESS_LENGTH, message: `企业地址不能超过${MAX_ADDRESS_LENGTH}个字符` }
					],
					buyerMobileNum: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								console.log(value, computeds.isSpecialInvoice.value);
								if (!value) {
									if (computeds.isSpecialInvoice.value) {
										return Promise.reject('请输入企业电话');
									} else {
										return Promise.resolve();
									}
								}

								if (!PHONE_PATTERN.test(value)) {
									// 测试电话
									if (PhoneRegExp.test(value)) {
										return Promise.resolve();
									}

									return Promise.reject('请输入正确的电话号码格式（手机号11位或固定电话8位）');
								}

								return Promise.resolve();
							}
						}
					]
				};
			})
		};

		// 表单验证规则
		const formRules = {
			receiveEmail: [
				{ required: true, message: '请输入交付邮箱' },
				{ pattern: EMAIL_PATTERN, message: '请输入正确的邮箱格式' }
			],
			invoiceType: [{ required: true, message: '请选择发票类型' }],
			invoiceAmount: [
				{
					required: true,
					validator(rule: any, value: number) {
						if (!value || value <= 0) {
							return Promise.reject('请输入有效的开票金额');
						}
						if (computeds.canEditInvoiceAmount.value && value > computeds.maxInvoiceAmount.value) {
							return Promise.reject(
								`开票金额不能超过订单可开票金额：${computeds.maxInvoiceAmount.value}元`
							);
						}
						return Promise.resolve();
					}
				}
			]
			// 移除 orderSelectList 的校验，改为在子组件中单独校验每个下拉框
		};

		const constants = {
			HEADER_TYPE_OPTIONS,
			INVOICE_TYPE_OPTIONS,
			MAX_ADDRESS_LENGTH
		};

		const methods = {
			// 显示弹窗
			show(orderNos: string[], invoiceBaseInfo: ApplyInvoicePreviewResponse) {
				state.orderNos = orderNos;
				state.isHistoryOrder = false;

				// 设置表单初始值
				state.formData = Object.assign({}, state.formData, {
					// 发票抬头
					buyerName: invoiceBaseInfo.buyerName,
					// 税号
					buyerIdNum: invoiceBaseInfo.buyerIdNum,
					// 开票金额
					invoiceAmount: invoiceBaseInfo.invoiceAmount,
					// 默认选择企业抬头
					buyerType: BuyerTypeEnum.COMPANY,
					// 默认选择普通发票
					invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON,
					// 重置订单选择列表
					orderSelectList: []
				});

				// 重置订单选择列表
				state.orderSelectList = [];

				state.visible = true;
			},

			// 老订单弹窗
			showOldOrder() {
				state.orderNos = [];
				state.isHistoryOrder = true;

				// 设置表单初始值
				state.formData = Object.assign({}, state.formData, {
					// 发票抬头
					buyerName: '',
					// 税号
					buyerIdNum: '',
					// 开票金额
					invoiceAmount: 0,
					// 默认选择企业抬头
					buyerType: BuyerTypeEnum.COMPANY,
					// 默认选择普通发票
					invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON,
					// 重置订单选择列表
					orderSelectList: []
				});

				// 重置订单选择列表
				state.orderSelectList = [];

				methods.getOldOrderList();

				state.visible = true;
			},

			// 获取历史可开票订单
			getOldOrderList() {
				GetHistoryOrderListStore.request()
					.getData()
					.then(data => {
						console.log(data);
						state.historyOrderOptions = data.itemList;
					});
			},

			// 发票类型变化时的处理
			onInvoiceTypeChange() {
				// 切换到普通发票时，清空专用发票的必填字段验证错误
				if (state.formData.invoiceType === InvoiceTypeEnum.ELECTRONIC_COMMON) {
					formRef.value?.clearValidate([
						'buyerBankName',
						'buyerBankAccount',
						'buyerAddress',
						'buyerMobileNum'
					]);
				}
			},

			/**
			 * 订单选择变化处理
			 * @param orderSelectList 订单选择列表
			 */
			onOrderSelectChange(orderSelectList: OrderSelectItemModel[]) {
				// 清空开票金额的表单校验
				formRef.value?.clearValidate(['invoiceAmount']);

				// 同时更新 state 和 formData
				state.orderSelectList = orderSelectList;
				state.formData.orderSelectList = orderSelectList;

				// 如果有选中的订单，获取第一个订单的发票信息
				const firstSelectedOrder = orderSelectList.find(item => item.selectedOrderNo);
				if (firstSelectedOrder && state.isHistoryOrder) {
					const selectedOrder = state.historyOrderOptions.find(
						order => order.orderNo === firstSelectedOrder.selectedOrderNo
					);

					if (selectedOrder) {
						// 当只有一个订单时，设置初始金额为可开票金额，但允许用户修改
						if (orderSelectList.length === 1) {
							state.formData.invoiceAmount = selectedOrder.invoiceableAmount;
						}

						state.submitLoading = true;
						GetApplyInvoicePreviewStore.request({
							jxId: userProfileStore.data.jiaxiaoId,
							orderNos: [selectedOrder.orderNo],
							newOrder: false
						})
							.getData()
							.then(data => {
								state.formData.buyerName = data.buyerName;
								state.formData.buyerIdNum = data.buyerIdNum;
							})
							.catch(() => {
								state.formData.buyerName = '';
								state.formData.buyerIdNum = '';
							})
							.finally(() => {
								state.submitLoading = false;
							});
					}
				}
			},

			/**
			 * 总金额变化处理
			 * @param totalAmount 总金额
			 */
			onTotalAmountChange(totalAmount: number) {
				// 只有在多个订单或者不可编辑时才自动设置金额
				if (!computeds.canEditInvoiceAmount.value) {
					state.formData.invoiceAmount = totalAmount;
				}
			},

			// 提交表单
			async onSubmit() {
				try {
					// 同时校验主表单和订单选择器表单
					const validationPromises: Promise<boolean>[] = [];

					// 主表单校验
					const mainFormValidation = formRef.value
						?.validate()
						.then(() => {
							console.log('主表单校验通过');
							return true;
						})
						.catch(error => {
							console.log('主表单校验失败:', error);
							return false;
						});
					validationPromises.push(mainFormValidation);

					// 老订单模式下的订单选择器校验
					if (state.isHistoryOrder && orderSelectorRef.value) {
						const orderSelectValidation = orderSelectorRef.value.validateAll();
						validationPromises.push(orderSelectValidation);
					}

					// 等待所有校验完成
					const validationResults = await Promise.all(validationPromises);
					const allValid = validationResults.every(result => result === true);

					console.log('校验结果:', validationResults, '全部通过:', allValid);

					if (!allValid) {
						console.log('表单校验失败，停止提交');
						return;
					}

					// 老订单模式下的额外验证
					if (state.isHistoryOrder) {
						// 发票信息验证
						if (!state.formData.buyerName || !state.formData.buyerIdNum) {
							MUtils.toast('当前订单未查询到发票信息，无法开票，请联系客服处理', MESSAGE_TYPE.warning);
							return;
						}
					}

					state.submitLoading = true;

					const { ...formData } = state.formData;

					if (state.isHistoryOrder) {
						// 老订单模式：构建历史订单提交数据
						const selectedOrders = orderSelectorRef.value?.getSelectedOrders() || [];
						const historyFormData = MUtils.unDataProxy({
							...formData,
							orderAmounts: selectedOrders.map(order => ({
								orderNo: order.selectedOrderNo,
								// 如果只有一个订单且可编辑，使用用户输入的金额，否则使用订单的金额
								invoiceAmount: computeds.canEditInvoiceAmount.value
									? state.formData.invoiceAmount
									: order.invoiceAmount
							}))
						});
						delete historyFormData.orderNos;

						console.log('历史订单开票申请数据:', historyFormData);

						const response = await FetchHistoryOrderInvoiceStore.request(historyFormData).getData();
						console.log(response);

						if (response?.serialNo) {
							methods.onClose();
							emit('success');
							MUtils.confirm({
								title: '提交反馈',
								content: '本次开票申请已提交成功，请耐心等待审核！',
								confirmText: '我知道了'
							});
						}
					} else {
						// 新订单模式：构建普通提交数据
						const submitData: InvoiceApplicationFormModel = MUtils.unDataProxy({
							...formData,
							orderNos: state.orderNos
						});

						console.log('开票申请数据:', submitData);

						const response = await SubmitInvoiceApplicationStore.request(submitData).getData();
						console.log(response);

						if (response?.serialNo) {
							methods.onClose();
							emit('success');
							MUtils.confirm({
								title: '提交反馈',
								content: '本次开票申请已提交成功，请耐心等待审核！',
								confirmText: '我知道了'
							});
						}
					}
				} catch (error) {
					console.error('开票申请提交失败:', error);

					// 如果是表单验证错误，不需要额外处理，Ant Design Vue 会自动显示错误
					if (error && error.errorFields) {
						console.log('表单验证错误:', error.errorFields);
						// 表单验证错误，不显示 toast
						return;
					}

					// 其他错误显示 toast
					MUtils.toast('提交失败，请重试', MESSAGE_TYPE.error);
				} finally {
					state.submitLoading = false;
				}
			},

			// 关闭弹窗
			onClose() {
				state.visible = false;
				state.submitLoading = false;

				// 重置表单
				formRef.value?.resetFields();

				// 重置订单选择器
				orderSelectorRef.value?.reset();
				state.orderSelectList = [];
				state.formData.orderSelectList = [];

				emit('close');
			}
		};

		return {
			formRef,
			orderSelectorRef,
			...toRefs(state),
			...computeds,
			...constants,
			...methods,
			formRules
		};
	}
});
