/**
 * 开票订单选择器组件类型定义
 */

/**
 * 订单选择项接口
 */
export interface OrderSelectItemModel {
	/** 唯一标识 */
	id: string;
	/** 选中的订单号 */
	selectedOrderNo?: string;
	/** 开票金额 */
	invoiceAmount: number;
}

/**
 * 历史订单响应接口
 */
export interface HistoryOrderResponse {
	/** 账户明细Id */
	id: number;
	/** 订单号 */
	orderNo: string;
	/** 消费时间 */
	actionTime: string;
	/** 商品名称 */
	goodsName: string;
	/** 备注 */
	comment: string;
	/** 订单金额 */
	orderAmount: number;
	/** 可开票金额 */
	invoiceableAmount: number;
	/** 能否申请开蓝票 */
	enableApplyBlueInvoice: boolean;
	/** 创建时间 */
	createTime: number;
	/** 支付时间 */
	paidTime: number;
	/** 格式化名称（用于显示） */
	formatName?: string;
}

/**
 * 组件状态接口
 */
export interface InvoiceOrderSelectorState {
	/** 订单选择列表 */
	orderSelectList: OrderSelectItemModel[];
}
