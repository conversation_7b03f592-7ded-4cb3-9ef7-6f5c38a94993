import type { InvoiceApplicationFormModel } from '@/application/invoice-management/types';
import type { OrderSelectItemModel } from '@/application/invoice-management/components/Invoice-order-selector/types';

export interface StateModel {
	visible: boolean;
	submitLoading: boolean;
	// 订单号列表
	orderNos: string[];
	// 是否是老订单
	isHistoryOrder: boolean;
	historyOrderOptions: HistoryOrderResponse[];
	// 订单选择列表
	orderSelectList: OrderSelectItemModel[];
	// 表单数据
	formData: InvoiceApplicationFormModel;
}

export interface HistoryOrderResponse {
	/**
	 * 账户明细Id
	 */
	id: number;

	/**
	 * 订单号
	 */
	orderNo: string;

	/**
	 * 消费时间
	 */
	actionTime: string;

	/**
	 * 商品名称
	 */
	goodsName: string;

	/**
	 * 备注
	 */
	comment: string;

	/**
	 * 订单金额
	 */
	orderAmount: number;

	/**
	 * 可开票金额
	 */
	invoiceableAmount: number;

	/**
	 * 能否申请开蓝票
	 */
	enableApplyBlueInvoice: boolean;

	createTime: number;
	paidTime: number;
}
